package com.aetrust.services;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.models.User;
import com.aetrust.models.User.UserAgentInfo;
import com.aetrust.types.Types.*;
import com.aetrust.utils.CryptoUtils;
import com.aetrust.utils.JwtUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.aetrust.repositories.UserRepository;
import com.aetrust.repositories.UserProfileRepository;
import com.aetrust.repositories.UserSecurityRepository;
import com.aetrust.repositories.UserIdentityVerificationRepository;
import com.aetrust.repositories.VerificationCodeRepository;
import com.aetrust.models.VerificationCode;
import com.aetrust.models.VerificationCode.VerificationType;
// import org.springframework.data.redis.core.RedisTemplate; // Redis disabled
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RegistrationService {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @lombok.Builder
    public static class RegistrationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String registrationId;
        private String nextStep;
        private String verificationCode;

        public static RegistrationResult success(String message, String registrationId) {
            return RegistrationResult.builder()
                .success(true)
                .message(message)
                .registrationId(registrationId)
                .nextStep("phone_verification")
                .build();
        }

        public static RegistrationResult error(String message, String errorCode) {
            return RegistrationResult.builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .build();
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @lombok.Builder
    public static class VerificationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String registrationId;
        private String nextStep;
        private String verificationCode;
        private boolean completed;
        private String accessToken;
        private String refreshToken;
        private Map<String, Object> userProfile;

        public static VerificationResult success(String message, String registrationId, String nextStep) {
            return VerificationResult.builder()
                .success(true)
                .message(message)
                .registrationId(registrationId)
                .nextStep(nextStep)
                .build();
        }

        public static VerificationResult error(String message, String errorCode) {
            return VerificationResult.builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .build();
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @lombok.Builder
    public static class IdVerificationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String userId;
        private String nextStep;

        public static IdVerificationResult success(String message, String userId, String nextStep) {
            return new IdVerificationResult(true, message, null, userId, nextStep);
        }

        public static IdVerificationResult error(String message, String errorCode) {
            return new IdVerificationResult(false, message, errorCode, null, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @lombok.Builder
    public static class PinSetupResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String userId;
        private String accessToken;
        private String refreshToken;
        private Map<String, Object> userProfile;

        public static PinSetupResult success(String message, String userId, String accessToken,
                                           String refreshToken, Map<String, Object> userProfile) {
            return new PinSetupResult(true, message, null, userId, accessToken, refreshToken, userProfile);
        }

        public static PinSetupResult error(String message, String errorCode) {
            return new PinSetupResult(false, message, errorCode, null, null, null, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @lombok.Builder
    public static class ResendResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String codeType;
        private String verificationCode;
        private Integer attemptsRemaining;

        public static ResendResult success(String message, String codeType, String verificationCode, Integer attemptsRemaining) {
            return ResendResult.builder()
                .success(true)
                .message(message)
                .codeType(codeType)
                .verificationCode(verificationCode)
                .attemptsRemaining(attemptsRemaining)
                .build();
        }

        public static ResendResult error(String message, String errorCode) {
            return ResendResult.builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .build();
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @lombok.Builder
    public static class StatusResult {
        private boolean found;
        private String registrationId;
        private String currentStep;
        private Integer stepNumber;
        private boolean phoneVerified;
        private boolean emailVerified;
        private boolean completed;
        private boolean isExpired;
        private String nextAction;
        private String instructions;
        private String expiresAt;

        public static StatusResult notFound() {
            return StatusResult.builder()
                .found(false)
                .build();
        }

        public static StatusResult error(String message) {
            return StatusResult.builder()
                .found(false)
                .instructions(message)
                .build();
        }

        public static StatusResult found(String registrationId, String currentStep, Integer stepNumber,
                                       boolean phoneVerified, boolean emailVerified, boolean completed,
                                       boolean isExpired, String nextAction, String instructions) {
            return StatusResult.builder()
                .found(true)
                .registrationId(registrationId)
                .currentStep(currentStep)
                .stepNumber(stepNumber)
                .phoneVerified(phoneVerified)
                .emailVerified(emailVerified)
                .completed(completed)
                .isExpired(isExpired)
                .nextAction(nextAction)
                .instructions(instructions)
                .build();
        }
    }


    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private UserSecurityRepository userSecurityRepository;

    @Autowired
    private UserIdentityVerificationRepository userIdentityVerificationRepository; 

    @Autowired
    private VerificationCodeRepository verificationCodeRepository;

    @Autowired
    private CacheService cacheService;
    
    @Autowired
    private CryptoUtils cryptoUtils;
    
    @Autowired
    private JwtUtils jwtUtils;
    
    @Autowired
    private NotificationService notificationService;

    // @Autowired
    // private ObjectMapper objectMapper;

    @Autowired
    private VerificationService verificationService;

    @Autowired
    private ValidationService validationService;

    @Autowired
    private FileUploadService fileUploadService;
    
    @Transactional
    public RegistrationResult initiateRegistration(RegistrationInitRequest request) {
        try {
            if (!validationService.isValidEmail(request.getEmail())) {
                return RegistrationResult.error("Registration failed", "REGISTRATION_FAILED");
            }

            if (!validationService.isValidPhone(request.getPhone())) {
                return RegistrationResult.error("Registration failed", "REGISTRATION_FAILED");
            }

            if (userRepository.existsByEmailAndDeletedAtIsNull(request.getEmail()) ||
                userRepository.existsByPhoneAndDeletedAtIsNull(request.getPhone())) {

                log.info("Registration attempted with existing credentials - email: {}, phone: {}",
                    cryptoUtils.maskSensitiveData(request.getEmail(), 2),
                    cryptoUtils.maskSensitiveData(request.getPhone(), 3));

                return RegistrationResult.error("Registration failed", "REGISTRATION_FAILED");
            }

            String registrationId = cryptoUtils.generateSecureToken(32);

            User newUser = new User();
            newUser.setEmail(request.getEmail());
            newUser.setPhone(request.getPhone());

            if (request.getPassword() != null && !request.getPassword().trim().isEmpty()) {
                newUser.setPassword(cryptoUtils.hashPassword(request.getPassword()));
            }

            newUser.setRole(UserRole.USER);
            newUser.setAccountStatus(AccountStatus.ACTIVE);
            newUser.setKycStatus(KycStatus.PENDING);
            newUser.setRegistrationStep(RegistrationStep.PHONE_VERIFICATION);
            newUser.setPhoneVerified(false);
            newUser.setEmailVerified(false);
            newUser.setRegistrationCompleted(false);
            newUser.setVerified(false);

            User savedUser = userRepository.save(newUser);
            log.info("User created with ID: {} for email: {}", savedUser.getId(),
                cryptoUtils.maskSensitiveData(savedUser.getEmail(), 3));

            // Create user profile immediately
            User.UserProfile profile = new User.UserProfile();
            profile.setUserId(savedUser.getId());
            profile.setUserUuid(savedUser.getUserUuid());
            profile.setFirstName(request.getFirstName());
            profile.setLastName(request.getLastName());
            profile.setDateOfBirth(request.getDateOfBirth());
            userProfileRepository.save(profile);

            registrationId = savedUser.getId().toString();

            String verificationCode = cryptoUtils.generateOTP(6);
            VerificationCode phoneVerification = VerificationCode.builder()
                .userIdentifier(request.getPhone())
                .code(cryptoUtils.hashVerificationCode(verificationCode))
                .verificationType(VerificationType.SMS)
                .createdAt(LocalDateTime.now())
                .expiresAt(LocalDateTime.now().plusMinutes(15))
                .attemptsCount(0)
                .isUsed(false)
                .ipAddress(request.getIpAddress())
                .userAgent(request.getUserAgent())
                .platform(request.getPlatform())
                .build();

            verificationCodeRepository.save(phoneVerification);

            boolean smsSent = notificationService.sendSMS(request.getPhone(),
                "Your AeTrust verification code is: " + verificationCode);

            log.info("Registration initiated for phone: {}",
                cryptoUtils.maskSensitiveData(request.getPhone(), 3));

            if (!smsSent) {
    
                return RegistrationResult.builder()
                    .success(true)
                    .message("registration initiated (SMS service unavailable)")
                    .registrationId(registrationId)
                    .nextStep(RegistrationStep.PHONE_VERIFICATION.getValue())
                    .verificationCode(verificationCode) // Include code 
                    .build();
            }

            return RegistrationResult.success("registration initiated", registrationId);
                
        } catch (Exception error) {
            log.error("Error initiating registration: {}", error.getMessage());
            return RegistrationResult.error("registration initiation failed", "INTERNAL_ERROR");
        }
    }
    
    @Transactional
    public VerificationResult verifyPhone(PhoneVerificationRequest request) {
        try {
            if (!validationService.isValidPhone(request.getPhone()) ||
                !validationService.isValidOtpCode(request.getCode())) {
                return VerificationResult.error("Verification failed", "VERIFICATION_FAILED");
            }

            VerificationService.VerificationResult result = verificationService.verifyCode(
                request.getPhone(), request.getCode(), VerificationType.SMS);

            log.info("VerificationService result for {}: Success={}, Message={}",
                request.getPhone(), result.isSuccess(), result.getMessage());

            if (!result.isSuccess()) {
                log.warn("VerificationService failed for {}: {}", request.getPhone(), result.getMessage());
                return VerificationResult.error("Verification failed", "VERIFICATION_FAILED");
            }

            // Find user by phone instead of using sessions
            Optional<User> userOpt = userRepository.findByPhoneAndDeletedAtIsNull(request.getPhone());

            log.info("User lookup for {}: Found={}", request.getPhone(), userOpt.isPresent());

            if (!userOpt.isPresent()) {
                log.warn("User not found for phone verification: {}", request.getPhone());
                return VerificationResult.error("user not found", "USER_NOT_FOUND");
            }

            User user = userOpt.get();
            user.setPhoneVerified(true);
            user.setRegistrationStep(RegistrationStep.EMAIL_VERIFICATION);
            userRepository.save(user);

            String emailCode = cryptoUtils.generateOTP(6);
            VerificationCode emailVerification = VerificationCode.builder()
                .userIdentifier(user.getEmail())
                .code(cryptoUtils.hashVerificationCode(emailCode))
                .verificationType(VerificationType.EMAIL)
                .createdAt(LocalDateTime.now())
                .expiresAt(LocalDateTime.now().plusMinutes(15))
                .attemptsCount(0)
                .isUsed(false)
                .build();

            verificationCodeRepository.save(emailVerification);

            NotificationService.NotificationResult emailResult = notificationService.sendEmail(user.getEmail(),
                "AeTrust Email Verification",
                "Your verification code is: " + emailCode);

            if (!emailResult.isSuccess()) {
                return VerificationResult.builder()
                    .success(true)
                    .message("phone verified (email service unavailable)")
                    .registrationId(user.getId().toString())
                    .nextStep(RegistrationStep.EMAIL_VERIFICATION.getValue())
                    .verificationCode(emailCode) // Include code when email fails
                    .build();
            }

            VerificationResult successResult = VerificationResult.success("phone verified", user.getId().toString(),
                RegistrationStep.EMAIL_VERIFICATION.getValue());

            log.info("Returning success result for {}: Success={}, Message={}, RegistrationId={}",
                request.getPhone(), successResult.isSuccess(), successResult.getMessage(), successResult.getRegistrationId());

            return successResult;
                
        } catch (Exception error) {
            log.error("Error verifying phone: {}", error.getMessage());
            return VerificationResult.error("phone verification failed", "INTERNAL_ERROR");
        }
    }
    
    @Transactional
    public VerificationResult verifyEmail(EmailVerificationRequest request) {
        try {
            if (!validationService.isValidEmail(request.getEmail()) ||
                !validationService.isValidOtpCode(request.getCode())) {
                return VerificationResult.error("Verification failed", "VERIFICATION_FAILED");
            }

            VerificationService.VerificationResult result = verificationService.verifyCode(
                request.getEmail(), request.getCode(), VerificationType.EMAIL);

            if (!result.isSuccess()) {
                return VerificationResult.error("Verification failed", "VERIFICATION_FAILED");
            }

            // Find user by email instead of using sessions
            Optional<User> userOpt = userRepository.findByEmailAndDeletedAtIsNull(request.getEmail());

            if (userOpt.isPresent()) {
                User user = userOpt.get();
                user.setEmailVerified(true);
                user.setRegistrationStep(RegistrationStep.PERSONAL_INFO);
                userRepository.save(user);

                return VerificationResult.success("email verification successful", user.getId().toString(),
                    "personal_info");
            }

            return VerificationResult.success("email verification successful", null, "personal_info");

        } catch (Exception error) {
            log.error("Error verifying email: {}", error.getMessage());
            return VerificationResult.error("email verification failed", "INTERNAL_ERROR");
        }
    }
    
    public IdVerificationResult submitIdVerification(IdVerificationRequest request) {
        try {
            // Find user by phone and email
            Optional<User> userOpt = userRepository.findByPhoneAndEmailAndDeletedAtIsNull(request.getPhone(), request.getEmail());
            if (!userOpt.isPresent()) {
                return IdVerificationResult.error("user not found", "USER_NOT_FOUND");
            }

            User user = userOpt.get();

            // Check if user is in the correct step
            if (!user.getRegistrationStep().equals(RegistrationStep.IDENTITY_VERIFICATION)) {
                return IdVerificationResult.error("complete previous steps first", "INVALID_STEP");
            }

            // Upload documents using the enhanced FileUploadService
            String idDocumentFrontUrl = null;
            String idDocumentBackUrl = null;
            String selfiePhotoUrl = null;

            if (request.getIdDocumentFront() != null && !request.getIdDocumentFront().trim().isEmpty()) {
                FileUploadService.UploadResult frontResult = fileUploadService.uploadBase64File(
                    request.getIdDocumentFront(),
                    FileUploadService.UploadType.ID_DOCUMENT_FRONT,
                    user.getUserUuid()
                );
                if (!frontResult.isSuccess()) {
                    return IdVerificationResult.error("Failed to upload ID document front: " + frontResult.getMessage(), "UPLOAD_FAILED");
                }
                idDocumentFrontUrl = frontResult.getFileUrl();
            }

            if (request.getIdDocumentBack() != null && !request.getIdDocumentBack().trim().isEmpty()) {
                FileUploadService.UploadResult backResult = fileUploadService.uploadBase64File(
                    request.getIdDocumentBack(),
                    FileUploadService.UploadType.ID_DOCUMENT_BACK,
                    user.getUserUuid()
                );
                if (!backResult.isSuccess()) {
                    return IdVerificationResult.error("Failed to upload ID document back: " + backResult.getMessage(), "UPLOAD_FAILED");
                }
                idDocumentBackUrl = backResult.getFileUrl();
            }

            if (request.getSelfiePhoto() != null && !request.getSelfiePhoto().trim().isEmpty()) {
                FileUploadService.UploadResult selfieResult = fileUploadService.uploadBase64File(
                    request.getSelfiePhoto(),
                    FileUploadService.UploadType.SELFIE_PHOTO,
                    user.getUserUuid()
                );
                if (!selfieResult.isSuccess()) {
                    return IdVerificationResult.error("Failed to upload selfie photo: " + selfieResult.getMessage(), "UPLOAD_FAILED");
                }
                selfiePhotoUrl = selfieResult.getFileUrl();
            }

            User.UserIdentityVerification idVerification = userIdentityVerificationRepository
                .findByUserIdAndDeletedAtIsNull(user.getId())
                .orElse(new User.UserIdentityVerification());

            idVerification.setUserId(user.getId());
            idVerification.setUserUuid(user.getUserUuid());
            idVerification.setIdType(IdType.valueOf(request.getIdType().toUpperCase()));
            idVerification.setIdNumber(request.getIdNumber());
            idVerification.setIdDocumentFront(idDocumentFrontUrl);
            idVerification.setIdDocumentBack(idDocumentBackUrl);
            idVerification.setSelfiePhoto(selfiePhotoUrl);
            idVerification.setVerificationStatus(VerificationStatus.PENDING);
            idVerification.setSubmittedAt(LocalDateTime.now());

            userIdentityVerificationRepository.save(idVerification);

            user.setKycStatus(KycStatus.PENDING);

            // Determine next step based on user role
            if ("agent".equals(user.getRole())) {
                user.setRegistrationStep(RegistrationStep.BUSINESS_VERIFICATION);
            } else {
                user.setRegistrationStep(RegistrationStep.TRANSACTION_PIN);
            }

            user.setUpdatedAt(LocalDateTime.now());
            userRepository.save(user);

            log.info("ID verification submitted for user: {}", cryptoUtils.maskSensitiveData(user.getPhone(), 3));

            String nextStep = "agent".equals(user.getRole()) ? "business_verification" : "transaction_pin";
            return IdVerificationResult.success("ID verification submitted successfully", user.getId().toString(), nextStep);

        } catch (Exception error) {
            log.error("Error submitting ID verification: {}", error.getMessage());
            return IdVerificationResult.error("ID verification failed", "INTERNAL_ERROR");
        }
    }

    public PinSetupResult setTransactionPin(SetTransactionPinRequest request) {
        try {
            Optional<User> userOpt = userRepository.findById(Long.parseLong(request.getUserId()));
            if (!userOpt.isPresent()) {
                return PinSetupResult.error("User not found", "USER_NOT_FOUND");
            }

            User user = userOpt.get();

            User.UserSecurity security = userSecurityRepository.findByUserIdAndDeletedAtIsNull(user.getId())
                .orElse(new User.UserSecurity());

            security.setUserId(user.getId());
            security.setUserUuid(user.getUserUuid());
            security.setTransactionPinSet(true);
            String pinHash = cryptoUtils.hashPassword(request.getPin());
            security.setTransactionPinHash(pinHash);
            userSecurityRepository.save(security);

            user.setRegistrationStep(RegistrationStep.COMPLETED);
            user.setRegistrationCompleted(true);
            userRepository.save(user);

            String accessToken = jwtUtils.generateToken(
                user.getId().toString(), user.getEmail(), user.getRole(),
                user.isVerified(), user.getKycStatus());
            String refreshToken = jwtUtils.generateRefreshToken(user.getId().toString());

            Map<String, Object> userProfile = createUserProfile(user);

            log.info("Registration completed for user: {}", user.getId());

            return PinSetupResult.success("Registration completed successfully",
                user.getId().toString(), accessToken, refreshToken, userProfile);

        } catch (Exception error) {
            log.error("Error setting transaction PIN: {}", error.getMessage());
            return PinSetupResult.error("PIN setup failed", "INTERNAL_ERROR");
        }
    }
    
    public StatusResult getRegistrationStatus(String identifier) {
        try {
            // Find user by phone or email instead of using sessions
            Optional<User> userOpt = userRepository.findByPhoneAndDeletedAtIsNull(identifier);
            if (!userOpt.isPresent()) {
                userOpt = userRepository.findByEmailAndDeletedAtIsNull(identifier);
            }

            if (!userOpt.isPresent()) {
                return StatusResult.notFound();
            }

            User user = userOpt.get();
            return StatusResult.builder()
                .found(true)
                .registrationId(user.getId().toString())
                .currentStep(user.getRegistrationStep().getValue())
                .stepNumber(getStepNumber(user.getRegistrationStep()))
                .phoneVerified(user.isPhoneVerified())
                .emailVerified(user.isEmailVerified())
                .completed(user.isRegistrationCompleted())
                .isExpired(false) // Users don't expire like sessions
                .build();

        } catch (Exception error) {
            log.error("Error getting registration status: {}", error.getMessage());
            return StatusResult.notFound();
        }
    }

    private Integer getStepNumber(RegistrationStep step) {
        if (step == null) return 1;
        switch (step) {
            case PHONE_VERIFICATION: return 1;
            case EMAIL_VERIFICATION: return 2;
            case TRANSACTION_PIN: return 3;
            case PERSONAL_INFO: return 4;
            case COMPLETED: return 5;
            default: return 1;
        }
    }


    public VerificationResult sendEmailVerificationCode(String email) {
        try {

            if (!userRepository.existsByEmailAndDeletedAtIsNull(email)) {
                log.info("Email verification attempted for non-existent email: {}",
                    cryptoUtils.maskSensitiveData(email, 2));
                return VerificationResult.error("Verification failed", "VERIFICATION_FAILED");
            }

            Optional<User> userOpt = userRepository.findByEmailAndDeletedAtIsNull(email);
            if (userOpt.isPresent() && userOpt.get().isEmailVerified()) {
                return VerificationResult.error("email already verified", "ALREADY_VERIFIED");
            }

            String emailCode = cryptoUtils.generateOTP(6);
            String emailCodeKey = "verification:email:" + email;
            cacheService.set(emailCodeKey, emailCode, 5, TimeUnit.MINUTES);

            NotificationService.NotificationResult emailResult = notificationService.sendEmail(email,
                "AeTrust Email Verification",
                "Your verification code is: " + emailCode);

            if (!emailResult.isSuccess()) {
                return VerificationResult.builder()
                    .success(true)
                    .message("email verification code generated (email service unavailable)")
                    .registrationId(null)
                    .nextStep("EMAIL_VERIFICATION")
                    .verificationCode(emailCode)
                    .build();
            }

            return VerificationResult.success("email verification code sent", null, "EMAIL_VERIFICATION");

        } catch (Exception error) {
            log.error("Error sending email verification code: {}", error.getMessage());
            return VerificationResult.error("failed to send email verification", "INTERNAL_ERROR");
        }
    }



    private Map<String, Object> createUserProfile(User user) {
        Map<String, Object> profile = new HashMap<>();
        profile.put("userId", user.getId());
        profile.put("email", user.getEmail());
        profile.put("phoneNumber", user.getPhone());

        User.UserProfile userProfile = userProfileRepository.findByUserIdAndDeletedAtIsNull(user.getId()).orElse(null);
        if (userProfile != null) {
            profile.put("firstName", userProfile.getFirstName());
            profile.put("lastName", userProfile.getLastName());
            profile.put("fullName", userProfile.getFullName());
        } else {
            profile.put("firstName", null);
            profile.put("lastName", null);
            profile.put("fullName", null);
        }

        profile.put("userRole", user.getRole().name());
        profile.put("isVerified", user.isVerified());
        profile.put("kycStatus", user.getKycStatus().name());
        profile.put("walletBalance", "0.00"); 
        profile.put("accountCreated", user.getCreatedAt());
        return profile;
    }


    public ResendResult resendVerificationCode(ResendVerificationRequest request) {
        try {
            String verificationType = request.getVerificationType();

            if (verificationType == null || verificationType.trim().isEmpty()) {
                return ResendResult.error("verification type is required", "INVALID_VERIFICATION_TYPE");
            }

            if ("email".equals(verificationType)) {
                if (request.getEmail() == null || request.getEmail().trim().isEmpty()) {
                    return ResendResult.error("email is required for email verification", "INVALID_EMAIL");
                }
                if (!validationService.isValidEmail(request.getEmail())) {
                    return ResendResult.error("invalid email format", "INVALID_EMAIL");
                }
            } else if ("sms".equals(verificationType) || "voice".equals(verificationType)) {
                if (request.getPhone() == null || request.getPhone().trim().isEmpty()) {
                    return ResendResult.error("phone number is required for SMS verification", "INVALID_PHONE");
                }
                if (!validationService.isValidPhone(request.getPhone())) {
                    return ResendResult.error("invalid phone format", "INVALID_PHONE");
                }
            } else {
                return ResendResult.error("invalid verification type. Use 'sms', 'email' or 'voice'", "INVALID_VERIFICATION_TYPE");
            }

            Optional<User> userOpt;
            if ("email".equals(verificationType)) {
                userOpt = userRepository.findByEmailAndDeletedAtIsNull(request.getEmail());
            } else {
                userOpt = userRepository.findByPhoneAndDeletedAtIsNull(request.getPhone());
            }

            if (!userOpt.isPresent()) {
                return ResendResult.error("user not found", "USER_NOT_FOUND");
            }

            User user = userOpt.get();

            if ("sms".equals(verificationType) || "voice".equals(verificationType)) {
                VerificationService.VerificationResult result = verificationService.sendPhoneVerification(user.getPhone(), user.getPhone());

                if (!result.isSuccess()) {
                    return ResendResult.error(result.getMessage(), result.getErrorCode());
                }

                return ResendResult.builder()
                    .success(true)
                    .message(result.getMessage())
                    .codeType("sms")
                    .verificationCode(result.getVerificationCode()) // Will be null if SMS sent successfully
                    .build();

            } else if ("email".equals(verificationType)) {
                if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
                    return ResendResult.error("user email not found", "EMAIL_NOT_FOUND");
                }

                VerificationService.VerificationResult result = verificationService.sendEmailVerification(user.getEmail(), user.getEmail());

                if (!result.isSuccess()) {
                    return ResendResult.error(result.getMessage(), result.getErrorCode());
                }

                return ResendResult.builder()
                    .success(true)
                    .message(result.getMessage())
                    .codeType("email")
                    .verificationCode(result.getVerificationCode()) // Will be null if email sent successfully
                    .build();
            }

        } catch (Exception error) {
            log.error("Error resending verification code: {}", error.getMessage());
            return ResendResult.error("failed to resend verification code", "INTERNAL_ERROR");
        }
        return null;
    }


    public List<VerificationCode> getUserVerificationCodes(String userIdentifier) {
        return verificationCodeRepository.findByUserIdentifierOrderByCreatedAtDesc(userIdentifier);
    }

    public boolean isRateLimited(String userIdentifier, VerificationType type) {
        LocalDateTime since = LocalDateTime.now().minusHours(1);
        long recentAttempts = verificationCodeRepository.countRecentAttempts(userIdentifier, type, since);
        return recentAttempts >= 5; 
    }

    @Transactional
    public int cleanupExpiredVerificationCodes() {
        try {
            LocalDateTime now = LocalDateTime.now();
            int deletedCount = verificationCodeRepository.deleteExpiredCodes(now);

            if (deletedCount > 0) {
                log.info("Cleaned up {} expired verification codes", deletedCount);
            }

            return deletedCount;
        } catch (Exception e) {
            log.error("Error cleaning up expired verification codes", e);
            return 0;
        }
    }

    @Transactional
    public int cleanupOldUsedVerificationCodes() {
        try {
            LocalDateTime cutoff = LocalDateTime.now().minusDays(7);
            int deletedCount = verificationCodeRepository.deleteOldUsedCodes(cutoff);

            if (deletedCount > 0) {
                log.info("Cleaned up {} old used verification codes", deletedCount);
            }

            return deletedCount;
        } catch (Exception e) {
            log.error("Error cleaning up old used verification codes", e);
            return 0;
        }
    }



    @Transactional
    public void cleanupUserVerificationCodes(String userIdentifier) {
        try {
            int deletedCount = verificationCodeRepository.deleteByUserIdentifier(userIdentifier);
            log.info("Cleaned up {} verification codes for user: {}",
                deletedCount, cryptoUtils.maskSensitiveData(userIdentifier, 3));
        } catch (Exception e) {
            log.error("Error cleaning up verification codes for user: {}",
                cryptoUtils.maskSensitiveData(userIdentifier, 3), e);
        }
    }

    @Transactional
    public PersonalInfoResult updatePersonalInfo(PersonalInfoRequest request) {
        try {

            Optional<User> userOpt = userRepository.findByIdAndDeletedAtIsNull(Long.parseLong(request.getRegistrationId()));

            if (!userOpt.isPresent()) {
                return PersonalInfoResult.error("user not found", "USER_NOT_FOUND");
            }

            User user = userOpt.get();

            user.setFirstName(request.getFirstName());
            user.setLastName(request.getLastName());
            user.setDateOfBirth(request.getDateOfBirth());
            user.setGender(request.getGender());
            user.setAddress(request.getAddress());

            String hashedPassword = cryptoUtils.hashPassword(request.getPassword());
            user.setPasswordHash(hashedPassword);

            user.setRegistrationStep(RegistrationStep.IDENTITY_VERIFICATION);
            user.setUpdatedAt(LocalDateTime.now());

            userRepository.save(user);

            log.info("Personal info updated for user: {}", cryptoUtils.maskSensitiveData(user.getPhone(), 3));

            Map<String, Object> progress = new HashMap<>();
            progress.put("currentStep", user.getRegistrationStep().getValue());
            progress.put("phoneVerified", user.isPhoneVerified());
            progress.put("emailVerified", user.isEmailVerified());
            progress.put("personalInfoCompleted", true);

            return PersonalInfoResult.builder()
                .success(true)
                .message("personal information updated successfully")
                .userId(user.getId().toString())
                .nextStep(RegistrationStep.IDENTITY_VERIFICATION.getValue())
                .registrationProgress(progress)
                .build();

        } catch (Exception error) {
            log.error("Error updating personal info: {}", error.getMessage());
            return PersonalInfoResult.error("failed to update personal information", "INTERNAL_ERROR");
        }
    }

    @Transactional
    public BusinessVerificationResult submitBusinessVerification(BusinessVerificationRequest request) {
        try {

            Optional<User> userOpt = userRepository.findByPhoneAndEmailAndDeletedAtIsNull(request.getPhone(), request.getEmail());

            if (!userOpt.isPresent()) {
                return BusinessVerificationResult.error("user not found", "USER_NOT_FOUND");
            }

            User user = userOpt.get();

            if (!user.getRegistrationStep().equals(RegistrationStep.BUSINESS_VERIFICATION) &&
                !user.getRegistrationStep().equals(RegistrationStep.IDENTITY_VERIFICATION)) {
                return BusinessVerificationResult.error("complete previous steps first", "INVALID_STEP");
            }

            user.setRole(UserRole.AGENT);

            // Create or update agent info
            UserAgentInfo agentInfo = user.getAgentInfo();
            if (agentInfo == null) {
                agentInfo = new UserAgentInfo();
                agentInfo.setUser(user);
                agentInfo.setUserUuid(user.getUserUuid());
                user.setAgentInfo(agentInfo);
            }

            // Upload business documents using the enhanced FileUploadService
            String businessDocumentUrl = null;
            String taxCertificateUrl = null;

            if (request.getBusinessCertificate() != null && !request.getBusinessCertificate().trim().isEmpty()) {
                FileUploadService.UploadResult businessDocResult = fileUploadService.uploadBase64File(
                    request.getBusinessCertificate(),
                    FileUploadService.UploadType.BUSINESS_DOCUMENT,
                    user.getUserUuid()
                );
                if (!businessDocResult.isSuccess()) {
                    return BusinessVerificationResult.error("Failed to upload business certificate: " + businessDocResult.getMessage(), "UPLOAD_FAILED");
                }
                businessDocumentUrl = businessDocResult.getFileUrl();
            }

            if (request.getTaxCertificate() != null && !request.getTaxCertificate().trim().isEmpty()) {
                FileUploadService.UploadResult taxCertResult = fileUploadService.uploadBase64File(
                    request.getTaxCertificate(),
                    FileUploadService.UploadType.BUSINESS_DOCUMENT,
                    user.getUserUuid()
                );
                if (!taxCertResult.isSuccess()) {
                    return BusinessVerificationResult.error("Failed to upload tax certificate: " + taxCertResult.getMessage(), "UPLOAD_FAILED");
                }
                taxCertificateUrl = taxCertResult.getFileUrl();
            }

            agentInfo.setBusinessName(request.getBusinessName());
            agentInfo.setBusinessType(request.getBusinessType());
            agentInfo.setBusinessRegistrationNumber(request.getRegistrationNumber());
            agentInfo.setTaxId(request.getTaxId());
            agentInfo.setBusinessDocument(businessDocumentUrl);
            agentInfo.setTaxCertificate(taxCertificateUrl);
            agentInfo.setVerificationStatus("PENDING");

            if (request.getBusinessAddress() != null) {
                StringBuilder addressBuilder = new StringBuilder();
                if (request.getBusinessAddress().getStreet() != null) {
                    addressBuilder.append(request.getBusinessAddress().getStreet());
                }
                if (request.getBusinessAddress().getCity() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(", ");
                    addressBuilder.append(request.getBusinessAddress().getCity());
                }
                if (request.getBusinessAddress().getState() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(", ");
                    addressBuilder.append(request.getBusinessAddress().getState());
                }
                if (request.getBusinessAddress().getCountry() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(", ");
                    addressBuilder.append(request.getBusinessAddress().getCountry());
                }
                if (request.getBusinessAddress().getPostalCode() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(" ");
                    addressBuilder.append(request.getBusinessAddress().getPostalCode());
                }
                agentInfo.setBusinessAddress(addressBuilder.toString());
            }

            user.setRegistrationStep(RegistrationStep.COMPLETED);
            user.setUpdatedAt(LocalDateTime.now());

            userRepository.save(user);

            log.info("Business verification submitted for user: {}", cryptoUtils.maskSensitiveData(user.getPhone(), 3));

            return BusinessVerificationResult.builder()
                .success(true)
                .message("business verification submitted successfully")
                .userId(user.getId().toString())
                .nextStep("completed")
                .verificationStatus("pending")
                .build();

        } catch (Exception error) {
            log.error("Error submitting business verification: {}", error.getMessage());
            return BusinessVerificationResult.error("failed to submit business verification", "INTERNAL_ERROR");
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PersonalInfoResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String userId;
        private String nextStep;
        private Map<String, Object> registrationProgress;

        public static PersonalInfoResult error(String message, String errorCode) {
            return PersonalInfoResult.builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .build();
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessVerificationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String userId;
        private String nextStep;
        private String verificationStatus;

        public static BusinessVerificationResult error(String message, String errorCode) {
            return BusinessVerificationResult.builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .build();
        }
    }


}
