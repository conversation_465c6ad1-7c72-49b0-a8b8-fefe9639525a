package com.aetrust.repositories;

import com.aetrust.models.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserAgentInfoRepository extends JpaRepository<User.UserAgentInfo, Long> {

    Optional<User.UserAgentInfo> findByUserIdAndDeletedAtIsNull(Long userId);
    Optional<User.UserAgentInfo> findByUserUuidAndDeletedAtIsNull(UUID userUuid);

    boolean existsByUserIdAndDeletedAtIsNull(Long userId);
    boolean existsByUserUuidAndDeletedAtIsNull(UUID userUuid);

    List<User.UserAgentInfo> findByIsActiveAndDeletedAtIsNull(boolean isActive);
    List<User.UserAgentInfo> findByVerificationStatusAndDeletedAtIsNull(String status);
    List<User.UserAgentInfo> findByBusinessTypeAndDeletedAtIsNull(String businessType);

    @Query("SELECT a FROM User$UserAgentInfo a WHERE a.commissionRate >= :minRate AND a.deletedAt IS NULL")
    List<User.UserAgentInfo> findByCommissionRateGreaterThanEqual(@Param("minRate") BigDecimal minRate);

    @Query("SELECT a FROM User$UserAgentInfo a WHERE a.totalCommissionEarned >= :minEarned AND a.deletedAt IS NULL")
    List<User.UserAgentInfo> findByTotalCommissionEarnedGreaterThanEqual(@Param("minEarned") BigDecimal minEarned);

    @Query("SELECT a FROM User$UserAgentInfo a WHERE a.performanceRating >= :minRating AND a.deletedAt IS NULL")
    List<User.UserAgentInfo> findByPerformanceRatingGreaterThanEqual(@Param("minRating") BigDecimal minRating);

    @Query("SELECT SUM(a.totalCommissionEarned) FROM User$UserAgentInfo a WHERE a.isActive = true AND a.deletedAt IS NULL")
    BigDecimal getTotalCommissionPaid();

    @Query("SELECT COUNT(a) FROM User$UserAgentInfo a WHERE a.verificationStatus = :status AND a.deletedAt IS NULL")
    long countByVerificationStatus(@Param("status") String status);

    @Query("SELECT a.businessType, COUNT(a) FROM User$UserAgentInfo a WHERE a.deletedAt IS NULL GROUP BY a.businessType")
    List<Object[]> getBusinessTypeDistribution();

    @Modifying
    @Query("UPDATE User$UserAgentInfo a SET a.deletedAt = :deletedAt WHERE a.userId = :userId")
    void softDeleteByUserId(@Param("userId") Long userId, @Param("deletedAt") LocalDateTime deletedAt);

    @Modifying
    @Query("UPDATE User$UserAgentInfo a SET a.deletedAt = :deletedAt WHERE a.userUuid = :userUuid")
    void softDeleteByUserUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);

    @Modifying
    @Query("UPDATE User$UserAgentInfo a SET a.monthlyCommission = 0 WHERE a.isActive = true AND a.deletedAt IS NULL")
    void resetMonthlyCommissions();
}
