package com.aetrust.dto;

import com.aetrust.validation.Validation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.time.LocalDate;
import java.math.BigDecimal;

public class RequestDTOs {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static abstract class BaseRequest {
        private String ipAddress;
        private String userAgent;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper=false)
    public static class RegistrationInitRequest extends BaseRequest {
        @JsonProperty("email")
        @NotBlank(message = "email is required")
        @Email(message = "invalid email format")
        @Size(max = 100, message = "email too long")
        private String email;

        @JsonProperty("phone")
        @NotBlank(message = "phone number is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;

        @JsonProperty("platform")
        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;

        @JsonProperty("firstName")
        @NotBlank(message = "first name is required")
        @Size(min = 2, max = 50, message = "first name must be between 2 and 50 characters")
        @Pattern(regexp = "^[a-zA-Z\\s\\-']*$", message = "first name contains invalid characters")
        private String firstName;

        @JsonProperty("lastName")
        @NotBlank(message = "last name is required")
        @Size(min = 2, max = 50, message = "last name must be between 2 and 50 characters")
        @Pattern(regexp = "^[a-zA-Z\\s\\-']*$", message = "last name contains invalid characters")
        private String lastName;

        @JsonProperty("dateOfBirth")
        @JsonFormat(pattern = "yyyy-MM-dd")
        @NotNull(message = "date of birth is required")
        @Past(message = "date of birth must be in the past")
        private LocalDate dateOfBirth;

        @JsonProperty("password")
        @NotBlank(message = "password is required")
        @ValidPassword(message = "password must be at least 8 characters with uppercase, lowercase, number and special character")
        private String password;

        private String ipAddress;
        private String userAgent;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PhoneVerificationRequest {
        @NotBlank(message = "phone number is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;
        
        @NotBlank(message = "verification code is required")
        @ValidVerificationCode(message = "verification code must be 6 digits")
        private String code;
        
        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SendEmailVerificationRequest {
        @NotBlank(message = "email is required")
        @Email(message = "invalid email format")
        private String email;

        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmailVerificationRequest {
        @NotBlank(message = "email is required")
        @Email(message = "invalid email format")
        private String email;

        @NotBlank(message = "verification code is required")
        @ValidVerificationCode(message = "verification code must be 6 digits")
        private String code;

        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IdVerificationRequest {
        @NotBlank(message = "phone is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;

        @NotBlank(message = "email is required")
        @Email(message = "invalid email format")
        private String email;

        @NotBlank(message = "ID type is required")
        @Pattern(regexp = "^(national_id|passport|drivers_license)$", message = "ID type must be national_id, passport, or drivers_license")
        private String idType;

        @NotBlank(message = "ID number is required")
        @Size(min = 5, max = 50, message = "ID number must be between 5 and 50 characters")
        private String idNumber;

        @NotBlank(message = "ID document front is required")
        private String idDocumentFront;

        // Optional back document - only required for certain ID types
        private String idDocumentBack;

        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SetTransactionPinRequest {
        @NotBlank(message = "user ID is required")
        private String userId;

        @NotBlank(message = "transaction PIN is required")
        @ValidPin(message = "PIN must be 4 digits")
        private String pin;

        @NotBlank(message = "PIN confirmation is required")
        @ValidPin(message = "PIN confirmation must be 4 digits")
        private String confirmPin;

        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;

        @AssertTrue(message = "PIN and confirmation must match")
        public boolean isPinMatching() {
            if (pin == null || confirmPin == null) {
                return true; // Let @NotBlank handle null validation
            }
            return pin.equals(confirmPin);
        }
    }
    
    @Data
    @EqualsAndHashCode(callSuper = false)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResendCodeRequest extends BaseRequest {
        @NotBlank(message = "phone is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;

        @NotBlank(message = "code type is required")
        @Pattern(regexp = "^(sms|email)$", message = "code type must be sms or email")
        private String codeType;

        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }



    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SetPinRequest {
        @NotBlank(message = "registration id is required")
        private String registrationId;

        @NotBlank(message = "transaction PIN is required")
        @ValidPin(message = "PIN must be 4 digits")
        private String pin;

        @NotBlank(message = "PIN confirmation is required")
        @ValidPin(message = "PIN confirmation must be 4 digits")
        private String confirmPin;

        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;

        @AssertTrue(message = "PIN and confirmation must match")
        public boolean isPinMatching() {
            if (pin == null || confirmPin == null) {
                return true; // Let @NotBlank handle null validation
            }
            return pin.equals(confirmPin);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KycDocumentRequest {
        @NotBlank(message = "phone number is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;

        @NotBlank(message = "ID type is required")
        @Pattern(regexp = "^(national_id|passport|drivers_license|voters_card)$",
                message = "ID type must be national_id, passport, drivers_license, or voters_card")
        private String idType;

        @NotBlank(message = "ID number is required")
        @Size(min = 5, max = 50, message = "ID number must be between 5 and 50 characters")
        private String idNumber;

        @NotBlank(message = "ID document front image is required")
        private String idDocumentFront; // base64

        @NotBlank(message = "ID document back image is required")
        private String idDocumentBack; // base64

        @NotBlank(message = "selfie photo is required")
        private String selfiePhoto; // base64

        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BiometricSetupRequest {
        @NotBlank(message = "phone number is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;

        @NotNull(message = "biometric enabled flag is required")
        private Boolean biometricEnabled;

        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AgentBusinessInfoRequest {
        @NotBlank(message = "phone number is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;

        @NotBlank(message = "business name is required")
        @Size(min = 2, max = 100, message = "business name must be between 2 and 100 characters")
        private String businessName;

        @NotBlank(message = "business type is required")
        @Pattern(regexp = "^(sole_proprietorship|partnership|corporation|cooperative|ngo)$",
                message = "business type must be sole_proprietorship, partnership, corporation, cooperative, or ngo")
        private String businessType;

        @NotBlank(message = "business registration number is required")
        @Size(min = 5, max = 50, message = "business registration number must be between 5 and 50 characters")
        private String businessRegistrationNumber;

        @NotBlank(message = "business address is required")
        @Size(min = 10, max = 200, message = "business address must be between 10 and 200 characters")
        private String businessAddress;

        @NotBlank(message = "business document is required")
        private String businessDocument; // base64 encoded document

        @NotBlank(message = "tax certificate is required")
        private String taxCertificate; // base64 encoded certificate

        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoginRequest {
        @NotBlank(message = "email is required")
        @Email(message = "invalid email format")
        private String email;
        
        @NotBlank(message = "password is required")
        private String password;
        
        @Pattern(regexp = "^\\d{6}$", message = "2FA code must be 6 digits")
        private String twoFactorCode;
        
        @ValidPlatform(message = "invalid platform")
        private String platform;
        
        private String deviceInfo;
        private String challengeToken;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateProfileRequest {
        @Size(min = 2, max = 50, message = "first name must be between 2 and 50 characters")
        @Pattern(regexp = "^[a-zA-Z\\s\\-']*$", message = "first name contains invalid characters")
        private String firstName;
        
        @Size(min = 2, max = 50, message = "last name must be between 2 and 50 characters")
        @Pattern(regexp = "^[a-zA-Z\\s\\-']*$", message = "last name contains invalid characters")
        private String lastName;
        
        @Size(max = 50, message = "username too long")
        @Pattern(regexp = "^[a-zA-Z0-9_]*$", message = "username can only contain letters, numbers and underscores")
        private String username;
        
        @Size(max = 500, message = "bio too long")
        private String bio;
        
        @Past(message = "date of birth must be in the past")
        private LocalDate dateOfBirth;

        @Size(max = 500, message = "profile picture URL too long")
        private String profilePicture;

        private AddressDto address;
        
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class AddressDto {
            @Size(max = 500, message = "street address too long")
            private String street;
            
            @Size(max = 100, message = "city name too long")
            private String city;
            
            @Size(max = 100, message = "state name too long")
            private String state;
            
            @Size(max = 100, message = "country name too long")
            private String country;
            
            @Size(max = 20, message = "postal code too long")
            private String postalCode;
        }
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdatePasswordRequest {
        @NotBlank(message = "current password is required")
        private String currentPassword;
        
        @NotBlank(message = "new password is required")
        @ValidPassword(message = "password must be at least 8 characters with uppercase, lowercase, number and special character")
        private String newPassword;
        
        @NotBlank(message = "password confirmation is required")
        private String confirmPassword;
        
        @AssertTrue(message = "new password and confirmation must match")
        public boolean isPasswordMatching() {
            return newPassword != null && newPassword.equals(confirmPassword);
        }
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UploadProfilePictureRequest {
        @NotBlank(message = "file data is required")
        private String fileData;
        
        @NotBlank(message = "file name is required")
        @Size(max = 255, message = "file name too long")
        private String fileName;
        
        @NotBlank(message = "file type is required")
        @Pattern(regexp = "^(image/jpeg|image/jpg|image/png)$", message = "only JPEG and PNG images are allowed")
        private String fileType;
        
        @Max(value = ********, message = "file size cannot exceed 10MB")
        private Long fileSize;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeleteAccountRequest {
        @NotBlank(message = "password is required")
        private String password;
        
        @NotBlank(message = "confirmation is required")
        @Pattern(regexp = "^DELETE$", message = "type DELETE to confirm account deletion")
        private String confirmation;
        
        @Size(max = 500, message = "reason too long")
        private String reason;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransferRequest {
        @NotNull(message = "amount is required")
        @DecimalMin(value = "0.01", message = "amount must be greater than 0")
        @DecimalMax(value = "1000000.00", message = "amount exceeds maximum limit")
        @Digits(integer = 10, fraction = 2, message = "amount format invalid")
        private BigDecimal amount;

        @NotBlank(message = "currency is required")
        @ValidCurrency(message = "invalid currency")
        private String currency;

        @NotBlank(message = "recipient phone is required")
        @ValidPhone(message = "recipient phone must be in international format")
        private String recipientPhone;

        @Size(max = 200, message = "description too long")
        private String description;

        @NotBlank(message = "transaction PIN is required")
        @ValidPin(message = "PIN must be 4 digits")
        private String pin;

        @ValidPlatform(message = "invalid platform")
        private String platform;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompletePersonalInfoRequest {
        @NotBlank(message = "first name is required")
        @Size(min = 2, max = 50, message = "first name must be between 2 and 50 characters")
        @Pattern(regexp = "^[a-zA-Z\\s\\-']+$", message = "first name contains invalid characters")
        private String firstName;

        @NotBlank(message = "last name is required")
        @Size(min = 2, max = 50, message = "last name must be between 2 and 50 characters")
        @Pattern(regexp = "^[a-zA-Z\\s\\-']+$", message = "last name contains invalid characters")
        private String lastName;

        @NotNull(message = "date of birth is required")
        @Past(message = "date of birth must be in the past")
        private LocalDate dateOfBirth;

        @Size(max = 10, message = "gender must be at most 10 characters")
        private String gender;

        @Size(max = 200, message = "address must be at most 200 characters")
        private String address;

        @NotBlank(message = "password is required")
        @ValidPassword(message = "password must be at least 8 characters with uppercase, lowercase, number and special character")
        private String password;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubmitIdentityRequest {
        @NotBlank(message = "document type is required")
        @Pattern(regexp = "^(passport|national_id|drivers_license|voters_card)$", message = "invalid document type")
        private String documentType;

        @NotBlank(message = "document number is required")
        @Size(min = 5, max = 50, message = "document number must be between 5 and 50 characters")
        private String documentNumber;

        @NotBlank(message = "issuing country is required")
        @Size(max = 100, message = "issuing country too long")
        private String issuingCountry;

        @Future(message = "expiry date must be in the future")
        private LocalDate expiryDate;

        @NotBlank(message = "front image is required")
        private String frontImage;

        private String backImage;

        @NotBlank(message = "selfie image is required")
        private String selfieImage;

        // backward compatibility
        public String getIdType() { return documentType; }
        public void setIdType(String idType) { this.documentType = idType; }

        public String getIdNumber() { return documentNumber; }
        public void setIdNumber(String idNumber) { this.documentNumber = idNumber; }

        public String getIdDocumentFront() { return frontImage; }
        public void setIdDocumentFront(String idDocumentFront) { this.frontImage = idDocumentFront; }

        public String getIdDocumentBack() { return backImage; }
        public void setIdDocumentBack(String idDocumentBack) { this.backImage = idDocumentBack; }

        public String getSelfiePhoto() { return selfieImage; }
        public void setSelfiePhoto(String selfiePhoto) { this.selfieImage = selfiePhoto; }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BiometricEnrollmentRequest {
        @NotBlank(message = "biometric type is required")
        @Pattern(regexp = "^(fingerprint|face|voice)$", message = "biometric type must be fingerprint, face or voice")
        private String biometricType;

        @NotBlank(message = "biometric data is required")
        private String biometricData;

        @NotBlank(message = "device info is required")
        private String deviceInfo;

        private String quality;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessVerificationRequest {
        @NotBlank(message = "phone is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;

        @NotBlank(message = "email is required")
        @Email(message = "invalid email format")
        private String email;

        @NotBlank(message = "business name is required")
        @Size(min = 2, max = 200, message = "business name must be between 2 and 200 characters")
        private String businessName;

        @NotBlank(message = "business registration number is required")
        @Size(min = 5, max = 50, message = "registration number must be between 5 and 50 characters")
        private String businessRegistrationNumber;

        @NotBlank(message = "business address is required")
        @Size(min = 10, max = 500, message = "business address must be between 10 and 500 characters")
        private String businessAddress;

        @NotBlank(message = "business document is required")
        private String businessDocument;

        @NotBlank(message = "bank account number is required")
        @Size(min = 8, max = 30, message = "bank account number must be between 8 and 30 characters")
        private String bankAccountNumber;

        @NotBlank(message = "bank name is required")
        @Size(min = 2, max = 100, message = "bank name must be between 2 and 100 characters")
        private String bankName;

        @NotBlank(message = "account holder name is required")
        @Size(min = 2, max = 100, message = "account holder name must be between 2 and 100 characters")
        private String accountHolderName;

        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }

    @Data
    @EqualsAndHashCode(callSuper = false)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResendVerificationRequest extends BaseRequest {
        @ValidPhone(message = "phone number must be in international format")
        private String phone;

        @Email(message = "email must be valid")
        private String email;

        @NotBlank(message = "verification type is required")
        @Pattern(regexp = "^(sms|email|voice)$", message = "verification type must be sms, email or voice")
        @JsonProperty("verificationType")
        private String verificationType;

        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;

        // Get the identifier based on verification type
        public String getIdentifier() {
            if ("email".equals(verificationType)) {
                return email;
            }
            return phone;
        }

        // Custom validation method
        @JsonIgnore
        public boolean isValid() {
            if ("email".equals(verificationType)) {
                return email != null && !email.trim().isEmpty();
            } else if ("sms".equals(verificationType) || "voice".equals(verificationType)) {
                return phone != null && !phone.trim().isEmpty();
            }
            return false;
        }
        public void setIdentifier(String identifier) { this.phone = identifier; }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateUserRequest {
        @NotBlank(message = "email is required")
        @Email(message = "invalid email format")
        private String email;

        @NotBlank(message = "phone is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;

        @NotBlank(message = "first name is required")
        @Size(min = 2, max = 50, message = "first name must be between 2 and 50 characters")
        private String firstName;

        @NotBlank(message = "last name is required")
        @Size(min = 2, max = 50, message = "last name must be between 2 and 50 characters")
        private String lastName;

        @NotBlank(message = "password hash is required")
        private String passwordHash;

        @NotBlank(message = "user type is required")
        @Pattern(regexp = "^(individual|business)$", message = "user type must be individual or business")
        private String userType;

        @NotBlank(message = "role is required")
        @Pattern(regexp = "^(user|agent|admin)$", message = "role must be user, agent or admin")
        private String role;

        @Past(message = "date of birth must be in the past")
        private LocalDate dateOfBirth;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateWalletRequest {
        @NotBlank(message = "user ID is required")
        private String userId;

        @NotBlank(message = "currency is required")
        @ValidCurrency(message = "invalid currency")
        private String currency;

        @NotNull(message = "initial balance is required")
        @DecimalMin(value = "0.00", message = "initial balance cannot be negative")
        private BigDecimal initialBalance;

        @Pattern(regexp = "^(MAIN|SAVINGS|BUSINESS)$", message = "wallet type must be MAIN, SAVINGS or BUSINESS")
        private String walletType = "MAIN";

        private boolean isDefault = true;

        public String getWalletType() { return walletType; }
        public void setWalletType(String walletType) { this.walletType = walletType; }

        public boolean isDefault() { return isDefault; }
        public void setDefault(boolean isDefault) { this.isDefault = isDefault; }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateBalanceRequest {
        @NotBlank(message = "wallet ID is required")
        private String walletId;

        @NotNull(message = "amount is required")
        @DecimalMin(value = "0.01", message = "amount must be greater than 0")
        private BigDecimal amount;

        @NotBlank(message = "operation type is required")
        @Pattern(regexp = "^(credit|debit)$", message = "operation type must be credit or debit")
        private String operationType;

        @NotBlank(message = "transaction reference is required")
        private String transactionRef;

        @Size(max = 200, message = "description too long")
        private String description;

        @Pattern(regexp = "^(TRANSFER|DEPOSIT|WITHDRAWAL|FEE|COMMISSION)$", message = "invalid transaction type")
        private String transactionType = "TRANSFER";

        public String getTransactionType() { return transactionType; }
        public void setTransactionType(String transactionType) { this.transactionType = transactionType; }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateSecurityRequest {
        @NotBlank(message = "user ID is required")
        private String userId;

        private Boolean twoFactorEnabled;

        private Boolean biometricEnabled;

        @ValidPin(message = "PIN must be 4 digits")
        private String transactionPin;

        @Size(max = 500, message = "security questions too long")
        private String securityQuestions;
    }

    // Auth-specific DTOs
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ForgotPasswordRequest {
        @NotBlank(message = "Email is required")
        @Email(message = "Invalid email format")
        @Size(max = 100, message = "Email too long")
        private String email;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResetPasswordRequest {
        @NotBlank(message = "Reset token is required")
        @Size(min = 32, max = 128, message = "Invalid reset token format")
        private String token;

        @NotBlank(message = "New password is required")
        @ValidPassword(message = "Password must be at least 8 characters with uppercase, lowercase, digit, and special character")
        private String newPassword;

        @NotBlank(message = "Password confirmation is required")
        private String confirmPassword;

        @AssertTrue(message = "Password and confirmation must match")
        public boolean isPasswordMatching() {
            return newPassword != null && newPassword.equals(confirmPassword);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RefreshTokenRequest {
        @NotBlank(message = "Refresh token is required")
        private String refreshToken;

        private String deviceInfo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LogoutRequest {
        private String accessToken;
        private String refreshToken;
        private boolean invalidateAllSessions = false;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TwoFactorSetupRequest {
        @NotBlank(message = "User ID is required")
        private String userId;

        @NotBlank(message = "2FA type is required")
        @Pattern(regexp = "^(totp|sms|email)$", message = "2FA type must be totp, sms, or email")
        private String twoFactorType;

        private String phoneNumber;
        private String email;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TwoFactorVerifyRequest {
        @NotBlank(message = "Challenge token is required")
        private String challengeToken;

        @NotBlank(message = "2FA code is required")
        @Pattern(regexp = "^\\d{6}$", message = "2FA code must be 6 digits")
        private String code;

        private String deviceInfo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CheckRegistrationStatusRequest {
        @NotBlank(message = "identifier is required")
        private String identifier; // phone or email

        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContinueRegistrationRequest {
        @NotBlank(message = "identifier is required")
        private String identifier; // phone or email

        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }
}
