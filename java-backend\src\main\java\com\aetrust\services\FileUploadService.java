package com.aetrust.services;

import com.aetrust.dto.RequestDTOs.UploadProfilePictureRequest;
import com.aetrust.utils.CryptoUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class FileUploadService {
    
    @Autowired
    private CryptoUtils cryptoUtils;
    
    @Value("${app.upload.directory:uploads}")
    private String uploadDirectory;
    
    @Value("${app.upload.max-file-size:10485760}") // 10MB default
    private long maxFileSize;
    
    @Value("${app.upload.allowed-types:image/jpeg,image/jpg,image/png}")
    private String allowedTypes;
    
    @Value("${app.cdn.base-url:https://cdn.aetrust.com}")
    private String cdnBaseUrl;
    
    private static final List<String> ALLOWED_EXTENSIONS = Arrays.asList("jpg", "jpeg", "png");
    private static final List<String> DANGEROUS_EXTENSIONS = Arrays.asList(
        "exe", "bat", "cmd", "com", "pif", "scr", "vbs", "js", "jar", "php", "asp", "jsp"
    );
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UploadResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String fileUrl;
        private String fileName;
        private long fileSize;
        
        public static UploadResult success(String message, String fileUrl, String fileName, long fileSize) {
            return new UploadResult(true, message, null, fileUrl, fileName, fileSize);
        }
        
        public static UploadResult error(String message, String errorCode) {
            return new UploadResult(false, message, errorCode, null, null, 0);
        }
    }
    

    public UploadResult uploadProfilePicture(MultipartFile file, UUID userId) {
        try {
            if (file == null || file.isEmpty()) {
                return UploadResult.error("No file provided", "FILE_REQUIRED");
            }
            
            if (file.getSize() > maxFileSize) {
                log.warn("File too large: {} bytes for user: {}", file.getSize(), userId);
                return UploadResult.error("File too large", "FILE_TOO_LARGE");
            }
            
            String contentType = file.getContentType();
            if (!isValidContentType(contentType)) {
                log.warn("Invalid content type: {} for user: {}", contentType, userId);
                return UploadResult.error("Invalid file type", "INVALID_FILE_TYPE");
            }
            
            String originalFilename = file.getOriginalFilename();
            if (!isValidFileExtension(originalFilename)) {
                log.warn("Invalid file extension: {} for user: {}", originalFilename, userId);
                return UploadResult.error("Invalid file extension", "INVALID_FILE_EXTENSION");
            }
            
            if (!isValidImageFile(file)) {
                log.warn("Invalid image file content for user: {}", userId);
                return UploadResult.error("Invalid image file", "INVALID_IMAGE_CONTENT");
            }
            
            String fileExtension = getFileExtension(originalFilename);
            String secureFileName = generateSecureFileName(userId, fileExtension);
            
            Path uploadPath = Paths.get(uploadDirectory, "profile-pictures");
            Files.createDirectories(uploadPath);
            
            Path filePath = uploadPath.resolve(secureFileName);
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            
            String fileUrl = generateSecureFileUrl("profile-pictures", secureFileName);
            
            log.info("Profile picture uploaded successfully for user: {}, file: {}", 
                userId, secureFileName);
            
            return UploadResult.success("File uploaded successfully", fileUrl, secureFileName, file.getSize());
            
        } catch (IOException e) {
            log.error("Error uploading file for user: {}: {}", userId, e.getMessage());
            return UploadResult.error("Failed to upload file", "UPLOAD_FAILED");
        } catch (Exception e) {
            log.error("Unexpected error uploading file for user: {}: {}", userId, e.getMessage());
            return UploadResult.error("Upload failed", "INTERNAL_ERROR");
        }
    }
    
    /**
     * Upload KYC document with additional security measures
     */
    public UploadResult uploadKycDocument(MultipartFile file, UUID userId, String documentType) {
        try {
            // Additional validation for KYC documents
            if (!isValidKycDocumentType(documentType)) {
                return UploadResult.error("Invalid document type", "INVALID_DOCUMENT_TYPE");
            }
            
            // Validate file presence
            if (file == null || file.isEmpty()) {
                return UploadResult.error("No file provided", "FILE_REQUIRED");
            }
            
            // Validate file size (larger limit for documents)
            long kycMaxSize = maxFileSize * 2; // 20MB for KYC documents
            if (file.getSize() > kycMaxSize) {
                log.warn("KYC document too large: {} bytes for user: {}", file.getSize(), userId);
                return UploadResult.error("Document too large", "FILE_TOO_LARGE");
            }
            
            // Validate content type (allow PDF and images for KYC)
            String contentType = file.getContentType();
            if (!isValidKycContentType(contentType)) {
                log.warn("Invalid KYC content type: {} for user: {}", contentType, userId);
                return UploadResult.error("Invalid document type", "INVALID_FILE_TYPE");
            }
            
            // Generate secure filename with document type
            String fileExtension = getFileExtension(file.getOriginalFilename());
            String secureFileName = generateKycFileName(userId, documentType, fileExtension);
            
            // Create upload directory
            Path uploadPath = Paths.get(uploadDirectory, "kyc-documents", userId.toString());
            Files.createDirectories(uploadPath);
            
            // Save file securely
            Path filePath = uploadPath.resolve(secureFileName);
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            
            // Generate secure URL (KYC documents should not be publicly accessible)
            String fileUrl = generateSecureFileUrl("kyc-documents/" + userId.toString(), secureFileName);
            
            log.info("KYC document uploaded successfully for user: {}, type: {}, file: {}", 
                userId, documentType, secureFileName);
            
            return UploadResult.success("Document uploaded successfully", fileUrl, secureFileName, file.getSize());
            
        } catch (IOException e) {
            log.error("Error uploading KYC document for user: {}: {}", userId, e.getMessage());
            return UploadResult.error("Failed to upload document", "UPLOAD_FAILED");
        } catch (Exception e) {
            log.error("Unexpected error uploading KYC document for user: {}: {}", userId, e.getMessage());
            return UploadResult.error("Upload failed", "INTERNAL_ERROR");
        }
    }
    
    /**
     * Validate content type against allowed types
     */
    private boolean isValidContentType(String contentType) {
        if (contentType == null) return false;
        List<String> allowed = Arrays.asList(allowedTypes.split(","));
        return allowed.contains(contentType.toLowerCase());
    }
    
    /**
     * Validate KYC document content type
     */
    private boolean isValidKycContentType(String contentType) {
        if (contentType == null) return false;
        return contentType.equals("application/pdf") || 
               contentType.equals("image/jpeg") || 
               contentType.equals("image/jpg") || 
               contentType.equals("image/png");
    }
    
    /**
     * Validate file extension
     */
    private boolean isValidFileExtension(String filename) {
        if (filename == null) return false;
        
        String extension = getFileExtension(filename).toLowerCase();
        
        // Check if extension is dangerous
        if (DANGEROUS_EXTENSIONS.contains(extension)) {
            return false;
        }
        
        // Check if extension is allowed
        return ALLOWED_EXTENSIONS.contains(extension);
    }
    
    /**
     * Validate KYC document type
     */
    private boolean isValidKycDocumentType(String documentType) {
        List<String> validTypes = Arrays.asList(
            "passport", "national_id", "drivers_license", "utility_bill", 
            "bank_statement", "business_license", "tax_certificate"
        );
        return validTypes.contains(documentType);
    }
    
    /**
     * Validate image file content by checking magic bytes
     */
    private boolean isValidImageFile(MultipartFile file) {
        try {
            byte[] fileBytes = file.getBytes();
            if (fileBytes.length < 4) return false;
            
            // Check JPEG magic bytes
            if (fileBytes[0] == (byte) 0xFF && fileBytes[1] == (byte) 0xD8) {
                return true;
            }
            
            // Check PNG magic bytes
            if (fileBytes[0] == (byte) 0x89 && fileBytes[1] == (byte) 0x50 && 
                fileBytes[2] == (byte) 0x4E && fileBytes[3] == (byte) 0x47) {
                return true;
            }
            
            return false;
        } catch (IOException e) {
            log.error("Error reading file bytes: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Get file extension from filename
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1);
    }
    
    /**
     * Generate secure filename for profile pictures
     */
    private String generateSecureFileName(UUID userId, String extension) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomToken = cryptoUtils.generateSecureToken(8);
        return String.format("profile_%s_%s_%s.%s", userId.toString(), timestamp, randomToken, extension);
    }
    
    /**
     * Generate secure filename for KYC documents
     */
    private String generateKycFileName(UUID userId, String documentType, String extension) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomToken = cryptoUtils.generateSecureToken(8);
        return String.format("kyc_%s_%s_%s_%s.%s", documentType, userId.toString(), timestamp, randomToken, extension);
    }
    
    /**
     * Generate secure file URL
     */
    private String generateSecureFileUrl(String directory, String filename) {
        return String.format("%s/%s/%s", cdnBaseUrl, directory, filename);
    }
}
