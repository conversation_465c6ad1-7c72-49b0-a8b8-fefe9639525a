package com.aetrust.models;

import com.aetrust.types.Types.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.UUID;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "users", indexes = {
    @Index(name = "idx_users_email", columnList = "email"),
    @Index(name = "idx_users_phone", columnList = "phone"),
    @Index(name = "idx_users_uuid", columnList = "userUuid"),
    @Index(name = "idx_users_role", columnList = "role"),
    @Index(name = "idx_users_status", columnList = "accountStatus"),
    @Index(name = "idx_users_kyc", columnList = "kycStatus"),
    @Index(name = "idx_users_created", columnList = "createdAt"),
    @Index(name = "idx_users_deleted", columnList = "deletedAt")
})
@EntityListeners(AuditingEntityListener.class)
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_uuid", unique = true, nullable = false, updatable = false)
    private UUID userUuid = UUID.randomUUID();

    @Column(unique = true, nullable = false)
    private String email;

    @JsonIgnore
    @Column(name = "password_hash")
    private String password;

    @JsonIgnore
    @Column(name = "transaction_pin_hash")
    private String transactionPin;

    @Column(unique = true, nullable = false)
    private String phone;

    @Column(unique = true)
    private String username;

    @Column(name = "first_name", length = 100)
    private String firstName;

    @Column(name = "last_name", length = 100)
    private String lastName;

    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;

    @Column(length = 10)
    private String gender;

    @Column(columnDefinition = "TEXT")
    private String address;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private UserRole role = UserRole.USER;

    @Enumerated(EnumType.STRING)
    @Column(name = "account_status", nullable = false)
    private AccountStatus accountStatus = AccountStatus.ACTIVE;

    @Enumerated(EnumType.STRING)
    @Column(name = "kyc_status", nullable = false)
    private KycStatus kycStatus = KycStatus.PENDING;

    @Enumerated(EnumType.STRING)
    @Column(name = "registration_step", nullable = false)
    private RegistrationStep registrationStep = RegistrationStep.PHONE_VERIFICATION;

    @Column(name = "is_verified")
    private boolean isVerified = false;

    @Column(name = "phone_verified")
    private boolean phoneVerified = false;

    @Column(name = "email_verified")
    private boolean emailVerified = false;

    @Column(name = "registration_completed")
    private boolean registrationCompleted = false;

    @CreatedDate
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    // Relationship to agent info
    @OneToOne(mappedBy = "userId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private UserAgentInfo agentInfo;

    public boolean isAgent() {
        return role == UserRole.AGENT;
    }

    public boolean isAdmin() {
        return role == UserRole.ADMIN || role == UserRole.SUPER_ADMIN;
    }

    public UserAgentInfo getAgentInfo() {
        return agentInfo;
    }

    public void setAgentInfo(UserAgentInfo agentInfo) {
        this.agentInfo = agentInfo;
    }

    @PrePersist
    protected void onCreate() {
        if (userUuid == null) {
            userUuid = UUID.randomUUID();
        }
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Entity
    @Table(name = "user_profiles", indexes = {
        @Index(name = "idx_user_profiles_user_id", columnList = "userId"),
        @Index(name = "idx_user_profiles_uuid", columnList = "userUuid"),
        @Index(name = "idx_user_profiles_name", columnList = "firstName, lastName")
    })
    @EntityListeners(AuditingEntityListener.class)
    public static class UserProfile {

        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private Long id;

        @Column(name = "user_id", nullable = false)
        private Long userId;

        @Column(name = "user_uuid", nullable = false)
        private UUID userUuid;

        @Column(name = "first_name", length = 100)
        private String firstName;

        @Column(name = "last_name", length = 100)
        private String lastName;

        @Column(name = "date_of_birth")
        private LocalDate dateOfBirth;

        @Column(columnDefinition = "TEXT")
        private String bio;

        @Column(name = "profile_picture", length = 500)
        private String profilePicture;

        @Column(length = 255)
        private String street;

        @Column(length = 100)
        private String city;

        @Column(length = 100)
        private String state;

        @Column(length = 100)
        private String country;

        @Column(name = "postal_code", length = 20)
        private String postalCode;

        @CreatedDate
        @Column(name = "created_at")
        private LocalDateTime createdAt;

        @LastModifiedDate
        @Column(name = "updated_at")
        private LocalDateTime updatedAt;

        @Column(name = "deleted_at")
        private LocalDateTime deletedAt;

        public String getFullName() {
            if (firstName == null && lastName == null) return null;
            if (firstName == null) return lastName;
            if (lastName == null) return firstName;
            return firstName + " " + lastName;
        }

        public String getFullAddress() {
            StringBuilder address = new StringBuilder();
            if (street != null && !street.trim().isEmpty()) {
                address.append(street);
            }
            if (city != null && !city.trim().isEmpty()) {
                if (address.length() > 0) address.append(", ");
                address.append(city);
            }
            if (state != null && !state.trim().isEmpty()) {
                if (address.length() > 0) address.append(", ");
                address.append(state);
            }
            if (country != null && !country.trim().isEmpty()) {
                if (address.length() > 0) address.append(", ");
                address.append(country);
            }
            if (postalCode != null && !postalCode.trim().isEmpty()) {
                if (address.length() > 0) address.append(" ");
                address.append(postalCode);
            }
            return address.toString();
        }
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Entity
    @Table(name = "user_wallets", indexes = {
        @Index(name = "idx_user_wallets_user_id", columnList = "userId"),
        @Index(name = "idx_user_wallets_uuid", columnList = "userUuid"),
        @Index(name = "idx_user_wallets_type", columnList = "walletType"),
        @Index(name = "idx_user_wallets_currency", columnList = "currency"),
        @Index(name = "idx_user_wallets_status", columnList = "status"),
        @Index(name = "idx_user_wallets_default", columnList = "isDefault")
    })
    @EntityListeners(AuditingEntityListener.class)
    public static class UserWallet {

        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private Long id;

        @Column(name = "user_id", nullable = false)
        private Long userId;

        @Column(name = "user_uuid", nullable = false)
        private UUID userUuid;

        @Enumerated(EnumType.STRING)
        @Column(name = "wallet_type", nullable = false)
        private WalletType walletType = WalletType.MAIN;

        @Column(nullable = false, length = 10)
        private String currency = "USD";

        @Column(precision = 15, scale = 2)
        private BigDecimal balance = BigDecimal.ZERO;

        @Column(name = "available_balance", precision = 15, scale = 2)
        private BigDecimal availableBalance = BigDecimal.ZERO;

        @Column(name = "pending_balance", precision = 15, scale = 2)
        private BigDecimal pendingBalance = BigDecimal.ZERO;

        @Column(name = "wallet_address", unique = true, length = 255)
        private String walletAddress;

        @Column(name = "is_default")
        private boolean isDefault = true;

        @Enumerated(EnumType.STRING)
        @Column(length = 20)
        private WalletStatus status = WalletStatus.ACTIVE;

        @Column(name = "daily_limit", precision = 15, scale = 2)
        private BigDecimal dailyLimit = new BigDecimal("10000.00");

        @Column(name = "monthly_limit", precision = 15, scale = 2)
        private BigDecimal monthlyLimit = new BigDecimal("100000.00");

        @Column(name = "transaction_limit", precision = 15, scale = 2)
        private BigDecimal transactionLimit = new BigDecimal("5000.00");

        @Column(name = "last_transaction_date")
        private LocalDateTime lastTransactionDate;

        @Column(name = "total_transactions")
        private Integer totalTransactions = 0;

        @Column(name = "total_credited", precision = 15, scale = 2)
        private BigDecimal totalCredited = BigDecimal.ZERO;

        @Column(name = "total_debited", precision = 15, scale = 2)
        private BigDecimal totalDebited = BigDecimal.ZERO;

        @CreatedDate
        @Column(name = "created_at")
        private LocalDateTime createdAt;

        @LastModifiedDate
        @Column(name = "updated_at")
        private LocalDateTime updatedAt;

        @Column(name = "deleted_at")
        private LocalDateTime deletedAt;

        public boolean canTransact() {
            return status == WalletStatus.ACTIVE && deletedAt == null;
        }

        public boolean hasInsufficientFunds(BigDecimal amount) {
            return availableBalance.compareTo(amount) < 0;
        }

        public boolean exceedsTransactionLimit(BigDecimal amount) {
            return amount.compareTo(transactionLimit) > 0;
        }

        public void updateBalance(BigDecimal amount, boolean isCredit) {
            if (isCredit) {
                balance = balance.add(amount);
                availableBalance = availableBalance.add(amount);
                totalCredited = totalCredited.add(amount);
            } else {
                balance = balance.subtract(amount);
                availableBalance = availableBalance.subtract(amount);
                totalDebited = totalDebited.add(amount);
            }
            totalTransactions++;
            lastTransactionDate = LocalDateTime.now();
        }

        public void freezeAmount(BigDecimal amount) {
            availableBalance = availableBalance.subtract(amount);
            pendingBalance = pendingBalance.add(amount);
        }

        public void unfreezeAmount(BigDecimal amount) {
            availableBalance = availableBalance.add(amount);
            pendingBalance = pendingBalance.subtract(amount);
        }
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Entity
    @Table(name = "user_security", indexes = {
        @Index(name = "idx_user_security_user_id", columnList = "userId"),
        @Index(name = "idx_user_security_uuid", columnList = "userUuid"),
        @Index(name = "idx_user_security_reset_token", columnList = "passwordResetToken"),
        @Index(name = "idx_user_security_email_token", columnList = "emailVerificationToken")
    })
    @EntityListeners(AuditingEntityListener.class)
    public static class UserSecurity {

        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private Long id;

        @Column(name = "user_id", nullable = false, unique = true)
        private Long userId;

        @Column(name = "user_uuid", nullable = false, unique = true)
        private UUID userUuid;

        @JsonIgnore
        @Column(name = "transaction_pin_hash")
        private String transactionPinHash;

        @Column(name = "transaction_pin_set")
        private boolean transactionPinSet = false;

        @Column(name = "biometric_enabled")
        private boolean biometricEnabled = false;

        @Column(name = "biometric_enrolled_at")
        private LocalDateTime biometricEnrolledAt;

        @Column(name = "two_factor_enabled")
        private boolean twoFactorEnabled = false;

        @Column(name = "login_attempts")
        private Integer loginAttempts = 0;

        @Column(name = "locked_until")
        private LocalDateTime lockedUntil;

        @JsonIgnore
        @Column(name = "password_reset_token")
        private String passwordResetToken;

        @Column(name = "password_reset_expires")
        private LocalDateTime passwordResetExpires;

        @JsonIgnore
        @Column(name = "email_verification_token")
        private String emailVerificationToken;

        @Column(name = "email_verification_expires")
        private LocalDateTime emailVerificationExpires;

        @JsonIgnore
        @Column(name = "phone_verification_code", length = 10)
        private String phoneVerificationCode;

        @Column(name = "phone_verification_expires")
        private LocalDateTime phoneVerificationExpires;

        @Column(name = "last_login")
        private LocalDateTime lastLogin;

        @Column(name = "last_login_ip")
        private String lastLoginIp;

        @Column(name = "last_login_platform", length = 50)
        private String lastLoginPlatform;

        @Column(name = "pin_attempts")
        private Integer pinAttempts = 0;

        @Column(name = "pin_locked_until")
        private LocalDateTime pinLockedUntil;

        @CreatedDate
        @Column(name = "created_at")
        private LocalDateTime createdAt;

        @LastModifiedDate
        @Column(name = "updated_at")
        private LocalDateTime updatedAt;

        @Column(name = "deleted_at")
        private LocalDateTime deletedAt;

        public boolean isAccountLocked() {
            return lockedUntil != null && lockedUntil.isAfter(LocalDateTime.now());
        }

        public boolean isPinLocked() {
            return pinLockedUntil != null && pinLockedUntil.isAfter(LocalDateTime.now());
        }

        public boolean isPasswordResetTokenValid() {
            return passwordResetToken != null &&
                   passwordResetExpires != null &&
                   passwordResetExpires.isAfter(LocalDateTime.now());
        }

        public boolean isEmailVerificationTokenValid() {
            return emailVerificationToken != null &&
                   emailVerificationExpires != null &&
                   emailVerificationExpires.isAfter(LocalDateTime.now());
        }

        public boolean isPhoneVerificationCodeValid() {
            return phoneVerificationCode != null &&
                   phoneVerificationExpires != null &&
                   phoneVerificationExpires.isAfter(LocalDateTime.now());
        }

        public void incrementLoginAttempts() {
            loginAttempts = (loginAttempts == null) ? 1 : loginAttempts + 1;
        }

        public void resetLoginAttempts() {
            loginAttempts = 0;
            lockedUntil = null;
        }

        public void incrementPinAttempts() {
            pinAttempts = (pinAttempts == null) ? 1 : pinAttempts + 1;
        }

        public void resetPinAttempts() {
            pinAttempts = 0;
            pinLockedUntil = null;
        }

        public void lockAccount(int minutes) {
            lockedUntil = LocalDateTime.now().plusMinutes(minutes);
        }

        public void lockPin(int minutes) {
            pinLockedUntil = LocalDateTime.now().plusMinutes(minutes);
        }

        public void updateLastLogin(String ipAddress, String platform) {
            lastLogin = LocalDateTime.now();
            lastLoginIp = ipAddress;
            lastLoginPlatform = platform;
        }
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Entity
    @Table(name = "user_preferences", indexes = {
        @Index(name = "idx_user_preferences_user_id", columnList = "userId"),
        @Index(name = "idx_user_preferences_uuid", columnList = "userUuid")
    })
    @EntityListeners(AuditingEntityListener.class)
    public static class UserPreferences {

        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private Long id;

        @Column(name = "user_id", nullable = false, unique = true)
        private Long userId;

        @Column(name = "user_uuid", nullable = false, unique = true)
        private UUID userUuid;

        @Column(length = 10)
        private String language = "en";

        @Column(length = 50)
        private String timezone = "UTC";

        @Column(length = 10)
        private String currency = "USD";

        @Column(length = 20)
        private String theme = "light";

        @Column(name = "email_notifications")
        private boolean emailNotifications = true;

        @Column(name = "sms_notifications")
        private boolean smsNotifications = true;

        @Column(name = "push_notifications")
        private boolean pushNotifications = true;

        @Column(name = "marketing_emails")
        private boolean marketingEmails = false;

        @Column(name = "security_alerts")
        private boolean securityAlerts = true;

        @Column(name = "transaction_alerts")
        private boolean transactionAlerts = true;

        @CreatedDate
        @Column(name = "created_at")
        private LocalDateTime createdAt;

        @LastModifiedDate
        @Column(name = "updated_at")
        private LocalDateTime updatedAt;

        @Column(name = "deleted_at")
        private LocalDateTime deletedAt;

        public boolean isNotificationEnabled(String type) {
            switch (type.toLowerCase()) {
                case "email":
                    return emailNotifications;
                case "sms":
                    return smsNotifications;
                case "push":
                    return pushNotifications;
                case "marketing":
                    return marketingEmails;
                case "security":
                    return securityAlerts;
                case "transaction":
                    return transactionAlerts;
                default:
                    return false;
            }
        }

        public void enableAllNotifications() {
            emailNotifications = true;
            smsNotifications = true;
            pushNotifications = true;
            securityAlerts = true;
            transactionAlerts = true;
        }

        public void disableAllNotifications() {
            emailNotifications = false;
            smsNotifications = false;
            pushNotifications = false;
            marketingEmails = false;
            securityAlerts = false;
            transactionAlerts = false;
        }
    }

  
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Entity
    @Table(name = "user_identity_verification", indexes = {
        @Index(name = "idx_identity_verification_user_id", columnList = "userId"),
        @Index(name = "idx_identity_verification_uuid", columnList = "userUuid"),
        @Index(name = "idx_identity_verification_status", columnList = "verificationStatus")
    })
    @EntityListeners(AuditingEntityListener.class)
    public static class UserIdentityVerification {

        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private Long id;

        @Column(name = "user_id", nullable = false, unique = true)
        private Long userId;

        @Column(name = "user_uuid", nullable = false, unique = true)
        private UUID userUuid;

        @Enumerated(EnumType.STRING)
        @Column(name = "id_type", length = 20)
        private IdType idType;

        @Column(name = "id_number", length = 100)
        private String idNumber;

        @Column(name = "id_document_front", length = 500)
        private String idDocumentFront;

        @Column(name = "id_document_back", length = 500)
        private String idDocumentBack;

        @Column(name = "selfie_photo", length = 500)
        private String selfiePhoto;

        @Enumerated(EnumType.STRING)
        @Column(name = "verification_status", length = 20)
        private VerificationStatus verificationStatus = VerificationStatus.PENDING;

        @Column(name = "verified_at")
        private LocalDateTime verifiedAt;

        @Column(name = "rejection_reason", columnDefinition = "TEXT")
        private String rejectionReason;

        @Column(name = "verification_notes", columnDefinition = "TEXT")
        private String verificationNotes;

        @Column(name = "verifier_id")
        private Long verifierId;

        @Column(name = "submitted_at")
        private LocalDateTime submittedAt;

        @CreatedDate
        @Column(name = "created_at")
        private LocalDateTime createdAt;

        @LastModifiedDate
        @Column(name = "updated_at")
        private LocalDateTime updatedAt;

        @Column(name = "deleted_at")
        private LocalDateTime deletedAt;

        public boolean isVerified() {
            return verificationStatus == VerificationStatus.APPROVED;
        }

        public boolean isPending() {
            return verificationStatus == VerificationStatus.PENDING;
        }

        public boolean isRejected() {
            return verificationStatus == VerificationStatus.REJECTED;
        }

        public boolean hasRequiredDocuments() {
            return idType != null &&
                   idNumber != null && !idNumber.trim().isEmpty() &&
                   idDocumentFront != null && !idDocumentFront.trim().isEmpty() &&
                   selfiePhoto != null && !selfiePhoto.trim().isEmpty();
        }

        public boolean requiresBackDocument() {
            return idType == IdType.NATIONAL_ID ||
                   idType == IdType.DRIVERS_LICENSE;
        }

        public boolean hasCompleteDocuments() {
            boolean hasRequired = hasRequiredDocuments();
            if (requiresBackDocument()) {
                return hasRequired &&
                       idDocumentBack != null &&
                       !idDocumentBack.trim().isEmpty();
            }
            return hasRequired;
        }

        public void approve(Long verifierId, String notes) {
            this.verificationStatus = VerificationStatus.APPROVED;
            this.verifiedAt = LocalDateTime.now();
            this.verifierId = verifierId;
            this.verificationNotes = notes;
            this.rejectionReason = null;
        }

        public void reject(Long verifierId, String reason, String notes) {
            this.verificationStatus = VerificationStatus.REJECTED;
            this.verifierId = verifierId;
            this.rejectionReason = reason;
            this.verificationNotes = notes;
            this.verifiedAt = null;
        }

        public void reset() {
            this.verificationStatus = VerificationStatus.PENDING;
            this.verifiedAt = null;
            this.verifierId = null;
            this.rejectionReason = null;
            this.verificationNotes = null;
        }
    }

   
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Entity
    @Table(name = "user_agent_info", indexes = {
        @Index(name = "idx_agent_info_user_id", columnList = "userId"),
        @Index(name = "idx_agent_info_uuid", columnList = "userUuid"),
        @Index(name = "idx_agent_info_active", columnList = "isActive"),
        @Index(name = "idx_agent_info_verification", columnList = "verificationStatus")
    })
    @EntityListeners(AuditingEntityListener.class)
    public static class UserAgentInfo {

        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private Long id;

        @Column(name = "user_id", nullable = false, unique = true)
        private Long userId;

        @Column(name = "user_uuid", nullable = false, unique = true)
        private UUID userUuid;

        @Column(name = "commission_rate", precision = 5, scale = 2)
        private BigDecimal commissionRate = BigDecimal.ZERO;

        @Column(name = "total_transactions")
        private Integer totalTransactions = 0;

        @Column(name = "total_commission_earned", precision = 15, scale = 2)
        private BigDecimal totalCommissionEarned = BigDecimal.ZERO;

        @Column(name = "is_active")
        private boolean isActive = true;

        @Column(name = "business_name")
        private String businessName;

        @Column(name = "business_type", length = 100)
        private String businessType;

        @Column(name = "business_registration_number", length = 100)
        private String businessRegistrationNumber;

        @Column(name = "business_address", columnDefinition = "TEXT")
        private String businessAddress;

        @Column(name = "business_document", length = 500)
        private String businessDocument;

        @Column(name = "tax_certificate", length = 500)
        private String taxCertificate;

        @Column(name = "verification_status", length = 20)
        private String verificationStatus = "PENDING";

        @Column(name = "monthly_commission", precision = 15, scale = 2)
        private BigDecimal monthlyCommission = BigDecimal.ZERO;

        @Column(name = "last_commission_date")
        private LocalDateTime lastCommissionDate;

        @Column(name = "performance_rating", precision = 3, scale = 2)
        private BigDecimal performanceRating = BigDecimal.ZERO;

        @Column(name = "successful_transactions")
        private Integer successfulTransactions = 0;

        @CreatedDate
        @Column(name = "created_at")
        private LocalDateTime createdAt;

        @LastModifiedDate
        @Column(name = "updated_at")
        private LocalDateTime updatedAt;

        @Column(name = "deleted_at")
        private LocalDateTime deletedAt;

        public boolean isVerified() {
            return "VERIFIED".equals(verificationStatus);
        }

        public boolean isPending() {
            return "PENDING".equals(verificationStatus);
        }

        public boolean isRejected() {
            return "REJECTED".equals(verificationStatus);
        }

        public BigDecimal getSuccessRate() {
            if (totalTransactions == null || totalTransactions == 0) {
                return BigDecimal.ZERO;
            }
            return new BigDecimal(successfulTransactions)
                    .divide(new BigDecimal(totalTransactions), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100));
        }

        public void addTransaction(BigDecimal commissionAmount, boolean successful) {
            totalTransactions = (totalTransactions == null) ? 1 : totalTransactions + 1;

            if (successful) {
                successfulTransactions = (successfulTransactions == null) ? 1 : successfulTransactions + 1;

                if (commissionAmount != null && commissionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    totalCommissionEarned = totalCommissionEarned.add(commissionAmount);
                    monthlyCommission = monthlyCommission.add(commissionAmount);
                    lastCommissionDate = LocalDateTime.now();
                }
            }

            updatePerformanceRating();
        }

        private void updatePerformanceRating() {
            BigDecimal successRate = getSuccessRate();
            if (successRate.compareTo(new BigDecimal(95)) >= 0) {
                performanceRating = new BigDecimal("5.00");
            } else if (successRate.compareTo(new BigDecimal(90)) >= 0) {
                performanceRating = new BigDecimal("4.50");
            } else if (successRate.compareTo(new BigDecimal(85)) >= 0) {
                performanceRating = new BigDecimal("4.00");
            } else if (successRate.compareTo(new BigDecimal(80)) >= 0) {
                performanceRating = new BigDecimal("3.50");
            } else if (successRate.compareTo(new BigDecimal(75)) >= 0) {
                performanceRating = new BigDecimal("3.00");
            } else {
                performanceRating = new BigDecimal("2.50");
            }
        }

        public void resetMonthlyCommission() {
            monthlyCommission = BigDecimal.ZERO;
        }

        public boolean hasRequiredDocuments() {
            return businessName != null && !businessName.trim().isEmpty() &&
                   businessType != null && !businessType.trim().isEmpty() &&
                   businessRegistrationNumber != null && !businessRegistrationNumber.trim().isEmpty() &&
                   businessDocument != null && !businessDocument.trim().isEmpty();
        }
    }

   
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Entity
    @Table(name = "user_merchant_info", indexes = {
        @Index(name = "idx_merchant_info_user_id", columnList = "userId"),
        @Index(name = "idx_merchant_info_uuid", columnList = "userUuid"),
        @Index(name = "idx_merchant_info_api_key", columnList = "apiKey"),
        @Index(name = "idx_merchant_info_verification", columnList = "verificationStatus")
    })
    @EntityListeners(AuditingEntityListener.class)
    public static class UserMerchantInfo {

        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private Long id;

        @Column(name = "user_id", nullable = false, unique = true)
        private Long userId;

        @Column(name = "user_uuid", nullable = false, unique = true)
        private UUID userUuid;

        @Column(name = "business_name")
        private String businessName;

        @Column(name = "business_type", length = 100)
        private String businessType;

        @Column(name = "business_registration", length = 100)
        private String businessRegistration;

        @Column(name = "tax_id", length = 100)
        private String taxId;

        @Column(name = "merchant_category", length = 100)
        private String merchantCategory;

        @Column(length = 255)
        private String website;

        @Column(name = "business_description", columnDefinition = "TEXT")
        private String businessDescription;

        @Column(name = "verification_status", length = 20)
        private String verificationStatus = "PENDING";

        @Column(name = "api_key", unique = true)
        private String apiKey;

        @Column(name = "webhook_url", length = 500)
        private String webhookUrl;

        @Column(name = "settlement_account", length = 100)
        private String settlementAccount;

        @CreatedDate
        @Column(name = "created_at")
        private LocalDateTime createdAt;

        @LastModifiedDate
        @Column(name = "updated_at")
        private LocalDateTime updatedAt;

        @Column(name = "deleted_at")
        private LocalDateTime deletedAt;

        public boolean isVerified() {
            return "VERIFIED".equals(verificationStatus);
        }

        public boolean isPending() {
            return "PENDING".equals(verificationStatus);
        }

        public boolean isRejected() {
            return "REJECTED".equals(verificationStatus);
        }

        public boolean hasApiAccess() {
            return isVerified() && apiKey != null && !apiKey.trim().isEmpty();
        }

        public boolean hasRequiredInfo() {
            return businessName != null && !businessName.trim().isEmpty() &&
                   businessType != null && !businessType.trim().isEmpty() &&
                   businessRegistration != null && !businessRegistration.trim().isEmpty() &&
                   merchantCategory != null && !merchantCategory.trim().isEmpty();
        }

        public void generateApiKey() {
            this.apiKey = "ak_" + UUID.randomUUID().toString().replace("-", "");
        }

        public void revokeApiKey() {
            this.apiKey = null;
        }

        public boolean isWebhookConfigured() {
            return webhookUrl != null && !webhookUrl.trim().isEmpty();
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessAddress {
        private String street;
        private String city;
        private String state;
        private String country;
        private String postalCode;

        public String getFullAddress() {
            StringBuilder address = new StringBuilder();
            if (street != null && !street.trim().isEmpty()) {
                address.append(street);
            }
            if (city != null && !city.trim().isEmpty()) {
                if (address.length() > 0) address.append(", ");
                address.append(city);
            }
            if (state != null && !state.trim().isEmpty()) {
                if (address.length() > 0) address.append(", ");
                address.append(state);
            }
            if (country != null && !country.trim().isEmpty()) {
                if (address.length() > 0) address.append(", ");
                address.append(country);
            }
            if (postalCode != null && !postalCode.trim().isEmpty()) {
                if (address.length() > 0) address.append(" ");
                address.append(postalCode);
            }
            return address.toString();
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AgentInfo {
        private String businessName;
        private String businessType;
        private String registrationNumber;
        private String taxId;
        private String verificationStatus;
        private LocalDateTime submittedAt;
        private BusinessAddress businessAddress;

        public boolean isVerified() {
            return "VERIFIED".equals(verificationStatus);
        }

        public boolean isPending() {
            return "PENDING".equals(verificationStatus);
        }

        public boolean isRejected() {
            return "REJECTED".equals(verificationStatus);
        }
    }

    public void setPasswordHash(String hashedPassword) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'setPasswordHash'");
    }
}
